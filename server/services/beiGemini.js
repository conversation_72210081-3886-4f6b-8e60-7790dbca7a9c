const https = require('https');
const { URL } = require('url');

class BEIGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({
        apiKey: process.env.GEMINI_API_KEY,
        httpOptions: {
          timeout: 600000
        }
      });
    }
    return this.client;
  }

  async generateResponse(model, prompt, config) {
    return new Promise((resolve, reject) => {
      try {
        const requestUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
        const queryParams = `key=${process.env.GEMINI_API_KEY}`;
        const url = new URL(`${requestUrl}?${queryParams}`);

        const postData = JSON.stringify({
          system_instruction: { parts: [{ text: config.systemInstruction }] },
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: config.temperature,
            responseMimeType: config.responseMimeType
          }
        });

        const options = {
          hostname: url.hostname,
          port: url.port || 443,
          path: url.pathname + url.search,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
          },
          timeout: 600000 // 10 minutes timeout
        };

        const req = https.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });

          res.on('end', () => {
            try {
              if (res.statusCode !== 200) {
                reject(new Error(`HTTP error! status: ${res.statusCode}`));
                return;
              }

              const responseData = JSON.parse(data);
              resolve(responseData.candidates[0].content.parts[0].text);
            } catch (parseError) {
              console.error('Error parsing response:', parseError);
              reject(new Error('Failed to parse response from Gemini'));
            }
          });
        });

        req.on('error', (error) => {
          console.error('Request error:', error);
          reject(new Error('Failed to generate response from Gemini'));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout - Gemini API took too long to respond'));
        });

        req.setTimeout(600000); // 10 minutes timeout
        req.write(postData);
        req.end();

      } catch (error) {
        console.error('Error generating response:', error);
        reject(new Error('Failed to generate response from Gemini'));
      }
    });
  }

  async runBEIAnalysis(transcript, analysisPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized

      // Parse transcript if it's a JSON string
      let parsedTranscript;
      try {
        parsedTranscript = typeof transcript === 'string' ? JSON.parse(transcript) : transcript;
      } catch (parseError) {
        // If parsing fails, treat as plain text
        parsedTranscript = transcript;
      }

      // Convert transcript to readable format
      let formattedTranscript;
      if (Array.isArray(parsedTranscript)) {
        // Format as conversation transcript
        formattedTranscript = parsedTranscript.map(entry => {
          if (entry.speaker && entry.text) {
            return `${entry.speaker}: ${entry.text}`;
          }
          return entry.text || entry;
        }).join('\n\n');
      } else if (typeof parsedTranscript === 'object' && parsedTranscript.data) {
        // Handle nested data structure
        formattedTranscript = Array.isArray(parsedTranscript.data) 
          ? parsedTranscript.data.map(entry => `${entry.speaker}: ${entry.text}`).join('\n\n')
          : JSON.stringify(parsedTranscript.data);
      } else {
        formattedTranscript = typeof parsedTranscript === 'string' 
          ? parsedTranscript 
          : JSON.stringify(parsedTranscript);
      }

      const userPrompt = `
        Here is the interview transcript to analyze:
        
        ${formattedTranscript}
      `;

      const config = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisPrompt
      };

      console.log(`Current time: ${new Date().toISOString()}`);
      console.log('Running BEI analysis...');

      const analysisOutput = await this.generateResponse(
        'gemini-2.5-pro-preview-06-05',
        userPrompt,
        config
      );

      console.log(`Current time: ${new Date().toISOString()}`);
      console.log('BEI analysis completed.');

      return {
        analysis: {
          prompt: userPrompt,
          systemInstruction: analysisPrompt,
          output: analysisOutput
        },
        finalOutput: analysisOutput
      };
    } catch (error) {
      console.error('Error in BEI analysis:', error);
      throw error;
    }
  }
}

module.exports = new BEIGeminiService();
