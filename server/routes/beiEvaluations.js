const express = require('express');
const router = express.Router();
const beiGeminiService = require('../services/beiGemini');
const supabase = require('../supabaseClient');
const fs = require('fs').promises;
const path = require('path');

// Background processing function
async function processBEIEvaluationAsync(evaluationId, input, promptContent) {
  try {
    console.log(`Starting background processing for BEI evaluation ${evaluationId}`);

    // Run the BEI analysis
    const result = await beiGeminiService.runBEIAnalysis(input.transcript, promptContent);

    // Update the record with the result
    const { error: updateError } = await supabase
      .from('bei_evaluations')
      .update({
        output: result.finalOutput,
        details: result,
        status: 'completed'
      })
      .eq('id', evaluationId);

    if (updateError) {
      console.error('Error updating BEI evaluation:', updateError);
      // Update status to error
      await supabase
        .from('bei_evaluations')
        .update({ status: 'error' })
        .eq('id', evaluationId);
    } else {
      console.log(`BEI evaluation ${evaluationId} completed successfully`);
    }
  } catch (error) {
    console.error(`Error processing BEI evaluation ${evaluationId}:`, error);
    // Update status to error
    await supabase
      .from('bei_evaluations')
      .update({ status: 'error' })
      .eq('id', evaluationId);
  }
}

// GET all BEI evaluations
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('bei_evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching BEI evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch BEI evaluations' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /bei-evaluations:', error);
    res.status(500).json({ error: 'Failed to fetch BEI evaluations' });
  }
});

// GET specific BEI evaluation status
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('bei_evaluations')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching BEI evaluation status:', error);
      return res.status(500).json({ error: 'Failed to fetch BEI evaluation status' });
    }

    if (!data) {
      return res.status(404).json({ error: 'BEI evaluation not found' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /bei-evaluations/:id/status:', error);
    res.status(500).json({ error: 'Failed to fetch BEI evaluation status' });
  }
});

// PUT update BEI evaluation annotation
router.put('/:id/annotation', async (req, res) => {
  try {
    const { id } = req.params;
    const { annotation } = req.body;

    const { data, error } = await supabase
      .from('bei_evaluations')
      .update({ annotation })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating BEI evaluation annotation:', error);
      return res.status(500).json({ error: 'Failed to update annotation' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in PUT /bei-evaluations/:id/annotation:', error);
    res.status(500).json({ error: 'Failed to update annotation' });
  }
});

// POST run new BEI evaluation
router.post('/run', async (req, res) => {
  try {
    const { input } = req.body;

    if (!input) {
      return res.status(400).json({ error: 'Input is required' });
    }

    // Validate BEI input structure
    if (!input.transcript) {
      return res.status(400).json({
        error: 'Transcript is required'
      });
    }

    // Get BEI prompt from file system (default prompt)
    let promptContent;
    try {
      const promptPath = path.join(__dirname, '..', 'data', 'bei_analyze_prompt.txt');
      promptContent = await fs.readFile(promptPath, 'utf8');
    } catch (fileError) {
      console.error('Error reading BEI prompt file:', fileError);
      return res.status(500).json({ error: 'Failed to load BEI analysis prompt' });
    }

    // Create evaluation record
    const evaluationToInsert = {
      input,
      output: "in_progress",
      status: 'in_progress',
      timestamp: new Date().toISOString(),
      annotation: null,
      details: null
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('bei_evaluations')
      .insert([evaluationToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting BEI evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save BEI evaluation' });
    }

    // Start background processing
    processBEIEvaluationAsync(newEvaluation.id, input, promptContent);

    // Return the evaluation record immediately
    res.json(newEvaluation);
  } catch (error) {
    console.error('Error in POST /bei-evaluations/run:', error);
    res.status(500).json({ error: 'Failed to run BEI evaluation' });
  }
});

module.exports = router;
